import 'server-only';

import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { cache } from 'react';

import getSupabaseClientKeys from '~/core/supabase/get-supabase-client-keys';
import { Database } from '~/database.types';

/**
 * @name getSupabaseServerComponentClient
 * @description Get a Supabase client for use in the Server Components
 * @param params
 */
const getSupabaseServerComponentClient = cache(
  (
    params = {
      admin: false,
    },
  ) => {
    const keys = getSupabaseClientKeys();

    if (params.admin) {
      const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

      if (!serviceRoleKey) {
        throw new Error('Supabase Service Role Key not provided');
      }

      return createServerClient<Database>(keys.url, serviceRoleKey, {
        auth: {
          persistSession: false,
        },
        cookies: {},
      });
    }

    return createServerClient<Database>(keys.url, keys.anonKey, {
      cookies: getCookiesStrategy(),
    });
  },
);

export default getSupabaseServerComponentClient;

function getCookiesStrategy() {
  const cookieStore = cookies();

  return {
    get: (name: string) => {
      return cookieStore.get(name)?.value;
    },
      set: () => {
      // Setting cookies is not supported in Server Components directly.
      // Use route handlers or middleware if needed.
    },
    remove: () => {
      // Same as above.
    },
  };
}



// import 'server-only';
// import { createServerClient } from '@supabase/ssr';
// import { cookies } from 'next/headers';
// import { cache } from 'react';

// import getSupabaseClientKeys from '~/core/supabase/get-supabase-client-keys';
// import { Database } from '~/database.types';

// /**
//  * @name getSupabaseServerComponentClient
//  * @description Get a Supabase client for use in the Server Components
//  * @param params - Optional configuration (e.g., admin mode)
//  */
// const getSupabaseServerComponentClient = cache(
//   (
//     params: {
//       admin?: boolean;
//     } = {
//       admin: false,
//     }
//   ) => {
//     const keys = getSupabaseClientKeys();
//     const cookieStore = cookies();

//     const cookieStrategy = {
//       get(name: string) {
//         return cookieStore.get(name)?.value;
//       },
//       set(name: string, value: string, options: any) {
//         // No-op in Server Components
//       },
//       remove(name: string, options: any) {
//         // No-op in Server Components
//       },
//     };

//     const supabaseKey = params.admin
//       ? process.env.SUPABASE_SERVICE_ROLE_KEY
//       : keys.anonKey;

//     if (params.admin && !supabaseKey) {
//       throw new Error('Supabase Service Role Key not provided');
//     }

//     return createServerClient<Database>(keys.url, supabaseKey!, {
//       cookies: cookieStrategy,
//       auth: {
//         persistSession: !params.admin,
//       },
//     });
//   }
// );

// export default getSupabaseServerComponentClient;
