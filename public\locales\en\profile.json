{"generalTab": "My Details", "generalTabSubheading": "Manage your profile details", "emailTab": "Email", "emailTabTabSubheading": "Update your email address", "passwordTab": "Password", "passwordTabSubheading": "Update your password", "manageConnectedAccounts": "Connected Accounts", "manageConnectedAccountsSubheading": "Manage your connected accounts", "connectedAccounts": "Connected Accounts", "authenticationTab": "Authentication", "multiFactorAuth": "Multi-Factor Authentication", "multiFactorAuthSubheading": "Set up a MFA method to secure your account", "connectedAccountsSubheading": "Below are the accounts linked to your profile", "availableProviders": "Available Providers", "availableProvidersSubheading": "Click on the providers below to link your profile to the provider", "updateProfileSuccess": "Profile successfully updated", "updateProfileError": "Encountered an error. Please try again", "updatePasswordSuccess": "Password update request successful", "updatePasswordSuccessMessage": "Your password has been successfully updated!", "updatePasswordError": "Encountered an error. Please try again", "updatePasswordLoading": "Updating password...", "updateProfileLoading": "Updating profile...", "displayNameLabel": "Your Name", "emailLabel": "Email Address", "profilePictureLabel": "Your Photo", "updateProfileSubmitLabel": "Update Profile", "currentPassword": "Current Password", "newPassword": "New Password", "repeatPassword": "Repeat New Password", "yourPassword": "Your Password", "updatePasswordSubmitLabel": "Update Password", "newEmail": "Your New Email", "repeatEmail": "Repeat Email", "updateEmailSubmitLabel": "Update Email Address", "updateEmailSuccess": "Email update request successful", "updateEmailSuccessMessage": "We sent you an email to confirm your new email address. Please check your inbox and click on the link to confirm your new email address.", "updateEmailLoading": "Updating your email...", "updateEmailError": "Email not updated. Please try again", "passwordNotMatching": "Passwords do not match. Make sure you're using the correct password", "passwordNotChanged": "Your password has not changed", "emailsNotMatching": "Emails do not match. Make sure you're using the correct email", "updatingSameEmail": "The email chosen is the same as your current one", "cannotUpdateEmail": "You cannot update your email because your account is not linked to any.", "cannotUpdatePassword": "You cannot update your password because your account is not linked to any.", "unlinkActionLabel": "Unlink", "unlinkAccountModalHeading": "Unlink Account", "confirmUnlink": "You're about to unlink this account.", "confirmUnlinkSubmitLabel": "Yep, Unlink Account", "unlinkActionSuccess": "Account successfully unlinked", "unlinkActionError": "Sorry, we couldn't unlink this account", "unlinkActionLoading": "Unlinking account...", "linkActionSuccess": "Account successfully linked", "linkActionError": "Sorry, we couldn't link this account", "linkActionLoading": "Linking account...", "linkAccount": "Link Account", "connectWithProvider": "Connect with {{ provider }}", "connectedWithProvider": "Connected with {{ provider }}", "setupMfaButtonLabel": "Setup a new Factor", "multiFactorSetupError": "Sorry, there was an error while setting up your factor. Please try again.", "multiFactorAuthHeading": "Secure your account with Multi-Factor Authentication", "multiFactorAuthDescription": "Enable Multi-Factor Authentication to verify your identity for an extra layer of security to your account in case your password is stolen. In addition to entering your password, it requires you confirm your identity via SMS.", "multiFactorModalHeading": "Use your phone to scan the QR code below. Then enter the code generated.", "factorNameLabel": "A memorable name to identify this factor", "factorNameHint": "Use an easy-to-remember name to easily identify this factor in the future. Ex. iPhone 14", "factorNameSubmitLabel": "Set factor name", "unenrollTooltip": "Unenroll this factor", "unenrollingFactor": "Unenrolling factor...", "unenrollFactorSuccess": "Factor successfully unenrolled", "unenrollFactorError": "Unenrolling factor failed", "factorsListError": "Error loading factors list", "factorName": "Factor Name", "factorType": "Type", "factorStatus": "Status", "mfaEnabledSuccessTitle": "Multi-Factor authentication is enabled", "mfaEnabledSuccessDescription": "Congratulations! You have successfully enrolled in the multi factor authentication process. You will now be able to access your account with a combination of your password and an authentication code sent to your phone number.", "verificationCode": "Verification Code", "addEmailAddress": "Add Email address", "updatePhoneNumber": "Update Phone Number", "updatePhoneNumberSubheading": "Link your phone number to your account", "updatePhoneNumberLoading": "Updating phone number...", "updatePhoneNumberSuccess": "Phone number successfully updated", "updatePhoneNumberError": "Sorry, we weren't able to update your phone number", "phoneNumberLabel": "Phone Number", "addPhoneNumber": "Add Phone Number", "removePhoneNumber": "Remove Phone Number", "confirmRemovePhoneNumberDescription": "You're about to remove your phone number. You will not be able to use it to login to your account.", "confirmRemovePhoneNumber": "Yes, remove phone number", "verifyActivationCodeDescription": "Enter the verification code generated by your authenticator app", "loadingFactors": "Loading factors...", "enableMfaFactor": "Enable Factor", "disableMfaFactor": "Disable Factor", "qrCodeError": "Sorry, we weren't able to generate the QR code", "multiFactorSetupSuccess": "Factor successfully enrolled", "submitVerificationCode": "Submit Verification Code", "mfaEnabledSuccessAlert": "Multi-Factor authentication is enabled", "verifyingCode": "Verifying code...", "invalidVerificationCode": "Invalid verification code. Please try again", "unenrollFactorModalHeading": "Unenroll Factor", "unenrollFactorModalBody": "You're about to unenroll this factor. You will not be able to use it to login to your account.", "unenrollFactorModalButtonLabel": "Yes, unenroll factor", "selectFactor": "Choose a factor to verify your identity", "disableMfa": "Disable Multi-Factor Authentication", "disableMfaButtonLabel": "Disable MFA", "confirmDisableMfaButtonLabel": "Yes, disable MFA", "disablingMfa": "Disabling Multi-Factor Authentication. Please wait...", "disableMfaSuccess": "Multi-Factor Authentication successfully disabled", "disableMfaError": "Sorry, we encountered an error. MFA has not been disabled.", "sendingEmailVerificationLink": "Sending Email...", "sendEmailVerificationLinkSuccess": "Verification link successfully sent", "sendEmailVerificationLinkError": "Sorry, we weren't able to send you the email", "sendVerificationLinkSubmitLabel": "Send Verification Link", "sendVerificationLinkSuccessLabel": "Email sent! Check your Inbox", "verifyEmailAlertHeading": "Please verify your email to enable MFA", "verificationLinkAlertDescription": "Your email is not yet verified. Please verify your email to be able to set up Multi-Factor Authentication.", "authFactorName": "Factor Name (optional)", "authFactorNameHint": "Assign a name that helps you remember the phone number used", "loadingUser": "Loading user details. Please wait...", "linkPhoneNumber": "Link Phone Number", "dangerZone": "Danger Zone", "dangerZoneSubheading": "Some actions cannot be undone. Please be careful.", "deleteAccount": "Delete your Account", "deleteAccountDescription": "This will delete your account and the organizations you own. Furthermore, we will immediately cancel any active subscriptions. This action cannot be undone. You will be asked to confirm this action in the next step.", "deleteProfileConfirmationInputLabel": "Type DELETE to confirm", "deleteAccountErrorHeading": "Sorry, we couldn't delete your account", "needsReauthentication": "Reauthentication Required", "needsReauthenticationDescription": "You need to reauthenticate to change your password. Please sign out and sign in again to change your password."}