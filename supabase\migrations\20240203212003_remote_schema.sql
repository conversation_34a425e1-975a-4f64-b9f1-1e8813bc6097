create table "public"."copy_collections" (
    "id" bigint generated by default as identity not null,
    "user_id" uuid not null default auth.uid(),
    "created_at" timestamp with time zone not null default now(),
    "name" text not null default ''::text,
    "organization_id" bigint not null
);


create table "public"."external_apps" (
    "id" uuid not null,
    "name" text not null,
    "created_at" timestamp with time zone not null default now()
);


create table "public"."fields" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "name" text not null default ''::text,
    "field_tag" text not null default ''::text,
    "type" text not null default ''::text,
    "description" text default ''::text,
    "placeholder" text default ''::text,
    "default_value" text default ''::text,
    "options" text[],
    "is_required" boolean default true
);


create table "public"."generations_copies" (
    "created_at" timestamp with time zone not null default now(),
    "user_id" uuid not null default auth.uid(),
    "content" text not null,
    "collection_id" bigint not null,
    "openai_id" text not null,
    "organization_id" bigint not null,
    "id" bigint generated by default as identity not null,
    "template_id" uuid
);


create table "public"."organizations_usage" (
    "created_at" timestamp with time zone not null default now(),
    "tokens_limit" bigint not null default '0'::bigint,
    "tokens_generated" bigint not null default '0'::bigint,
    "organization_id" bigint not null
);


create table "public"."template_fields" (
    "field_id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "template_id" uuid not null
);


create table "public"."templates" (
    "created_at" timestamp with time zone not null default now(),
    "title" text not null,
    "description" text not null default ''::text,
    "image" text not null default ''::text,
    "category" text not null default ''::text,
    "prompt" text not null default ''::text,
    "isNew" boolean default true,
    "id" uuid not null default gen_random_uuid()
);


alter table "public"."subscriptions" add column "max_users" bigint not null default '1'::bigint;

CREATE UNIQUE INDEX copy_collections_pkey ON public.copy_collections USING btree (id, user_id, organization_id);

CREATE UNIQUE INDEX external_apps_pkey ON public.external_apps USING btree (id);

CREATE UNIQUE INDEX fields_field_tag_key ON public.fields USING btree (field_tag);

CREATE UNIQUE INDEX fields_pkey ON public.fields USING btree (id);

CREATE UNIQUE INDEX generations_copies_id_key ON public.generations_copies USING btree (id);

CREATE UNIQUE INDEX generations_copies_pkey ON public.generations_copies USING btree (collection_id, id);

CREATE UNIQUE INDEX organizations_usage_pkey ON public.organizations_usage USING btree (organization_id);

CREATE UNIQUE INDEX template_fields_pkey ON public.template_fields USING btree (field_id, template_id);

CREATE UNIQUE INDEX templates_pkey ON public.templates USING btree (id);

CREATE UNIQUE INDEX templates_uid_key ON public.templates USING btree (id);

CREATE UNIQUE INDEX user_collections_id_key ON public.copy_collections USING btree (id);

alter table "public"."copy_collections" add constraint "copy_collections_pkey" PRIMARY KEY using index "copy_collections_pkey";

alter table "public"."external_apps" add constraint "external_apps_pkey" PRIMARY KEY using index "external_apps_pkey";

alter table "public"."fields" add constraint "fields_pkey" PRIMARY KEY using index "fields_pkey";

alter table "public"."generations_copies" add constraint "generations_copies_pkey" PRIMARY KEY using index "generations_copies_pkey";

alter table "public"."organizations_usage" add constraint "organizations_usage_pkey" PRIMARY KEY using index "organizations_usage_pkey";

alter table "public"."template_fields" add constraint "template_fields_pkey" PRIMARY KEY using index "template_fields_pkey";

alter table "public"."templates" add constraint "templates_pkey" PRIMARY KEY using index "templates_pkey";

alter table "public"."copy_collections" add constraint "copy_collections_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE not valid;

alter table "public"."copy_collections" validate constraint "copy_collections_organization_id_fkey";

alter table "public"."copy_collections" add constraint "copy_collections_user_id_fkey" FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE not valid;

alter table "public"."copy_collections" validate constraint "copy_collections_user_id_fkey";

alter table "public"."copy_collections" add constraint "user_collections_id_key" UNIQUE using index "user_collections_id_key";

alter table "public"."fields" add constraint "fields_field_tag_key" UNIQUE using index "fields_field_tag_key";

alter table "public"."generations_copies" add constraint "generations_copies_collection_id_fkey" FOREIGN KEY (collection_id) REFERENCES copy_collections(id) ON DELETE CASCADE not valid;

alter table "public"."generations_copies" validate constraint "generations_copies_collection_id_fkey";

alter table "public"."generations_copies" add constraint "generations_copies_id_key" UNIQUE using index "generations_copies_id_key";

alter table "public"."generations_copies" add constraint "generations_copies_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE not valid;

alter table "public"."generations_copies" validate constraint "generations_copies_organization_id_fkey";

alter table "public"."generations_copies" add constraint "generations_copies_template_id_fkey" FOREIGN KEY (template_id) REFERENCES templates(id) ON DELETE SET NULL not valid;

alter table "public"."generations_copies" validate constraint "generations_copies_template_id_fkey";

alter table "public"."generations_copies" add constraint "generations_copies_user_id_fkey" FOREIGN KEY (user_id) REFERENCES users(id) not valid;

alter table "public"."generations_copies" validate constraint "generations_copies_user_id_fkey";

alter table "public"."organizations_usage" add constraint "organizations_usage_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE not valid;

alter table "public"."organizations_usage" validate constraint "organizations_usage_organization_id_fkey";

alter table "public"."template_fields" add constraint "template_fields_field_id_fkey" FOREIGN KEY (field_id) REFERENCES fields(id) ON DELETE CASCADE not valid;

alter table "public"."template_fields" validate constraint "template_fields_field_id_fkey";

alter table "public"."template_fields" add constraint "template_fields_template_id_fkey" FOREIGN KEY (template_id) REFERENCES templates(id) ON DELETE SET NULL not valid;

alter table "public"."template_fields" validate constraint "template_fields_template_id_fkey";

alter table "public"."templates" add constraint "templates_uid_key" UNIQUE using index "templates_uid_key";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.delete_secret(v_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
begin
  if current_setting('role') != 'service_role' then 
    raise exception 'Authentication required';
  end if;

  delete from vault.decrypted_secrets 
  where id = v_id;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.enroll_user_with_new_org(org_name text, user_id text, create_user boolean DEFAULT true)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
declare
  organization bigint;
  uid uuid;
begin
  insert into organizations(
    "name")
  values (
    org_name)
returning
  id,
  uuid into organization,
  uid;

  if create_user then
    insert into users(
      id,
      onboarded)
    values (
      user_id,
      true);
  end if;
  insert into memberships(
    user_id,
    organization_id,
    role)
  values (
    user_id,
    organization,
    2);
  return uid;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.insert_secret(name text, secret text)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
begin
  if current_setting('role') != 'service_role' then
    raise exception 'authentication required';
  end if;
 
  return vault.create_secret(secret, name);
end;
$function$
;

CREATE OR REPLACE FUNCTION public.read_id(v_secret text)
 RETURNS text
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
declare
  v_id text;
begin
  if current_setting('role') != 'service_role' then
    raise exception 'authentication required';
  end if;
 
  select id from vault.decrypted_secrets where secret =
  v_secret into v_id;
  return v_id;
end;
$function$
;

drop function if exists "public"."enroll_user_with_new_org"(org_name text, user_id text, create_user boolean);

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.enroll_user_with_new_org(org_name text, user_id uuid, create_user boolean DEFAULT true)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
declare
  organization bigint;
  uid uuid;
begin
  insert into organizations(
    "name")
  values (
    org_name)
returning
  id,
  uuid into organization,
  uid;

  if create_user then
    insert into users(
      id,
      onboarded)
    values (
      user_id,
      true);
  end if;
  insert into memberships(
    user_id,
    organization_id,
    role)
  values (
    user_id,
    organization,
    2);
  return uid;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.read_id(v_secret text)
 RETURNS text
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
declare
  v_id text;
begin
  if current_setting('role') != 'service_role' then
    raise exception 'authentication required';
  end if;
 
  select id from vault.decrypted_secrets where decrypted_secret =
  v_secret into v_id;
  return v_id;
end;
$function$
;


drop function if exists "public"."get_user_id_by_email"(email text);

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.get_user_id_by_email(email text)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    uid uuid;
BEGIN
    SELECT id INTO uid FROM auth.users au WHERE au.email = get_user_id_by_email.email LIMIT 1;
    RETURN uid;
END;
$function$
;

alter table "public"."templates" alter column "isNew" set not null;


insert into storage.buckets (id, name, PUBLIC)
  values ('templates', 'templates', true);


grant delete on table "public"."copy_collections" to "anon";

grant insert on table "public"."copy_collections" to "anon";

grant references on table "public"."copy_collections" to "anon";

grant select on table "public"."copy_collections" to "anon";

grant trigger on table "public"."copy_collections" to "anon";

grant truncate on table "public"."copy_collections" to "anon";

grant update on table "public"."copy_collections" to "anon";

grant delete on table "public"."copy_collections" to "authenticated";

grant insert on table "public"."copy_collections" to "authenticated";

grant references on table "public"."copy_collections" to "authenticated";

grant select on table "public"."copy_collections" to "authenticated";

grant trigger on table "public"."copy_collections" to "authenticated";

grant truncate on table "public"."copy_collections" to "authenticated";

grant update on table "public"."copy_collections" to "authenticated";

grant delete on table "public"."copy_collections" to "service_role";

grant insert on table "public"."copy_collections" to "service_role";

grant references on table "public"."copy_collections" to "service_role";

grant select on table "public"."copy_collections" to "service_role";

grant trigger on table "public"."copy_collections" to "service_role";

grant truncate on table "public"."copy_collections" to "service_role";

grant update on table "public"."copy_collections" to "service_role";

grant delete on table "public"."external_apps" to "anon";

grant insert on table "public"."external_apps" to "anon";

grant references on table "public"."external_apps" to "anon";

grant select on table "public"."external_apps" to "anon";

grant trigger on table "public"."external_apps" to "anon";

grant truncate on table "public"."external_apps" to "anon";

grant update on table "public"."external_apps" to "anon";

grant delete on table "public"."external_apps" to "authenticated";

grant insert on table "public"."external_apps" to "authenticated";

grant references on table "public"."external_apps" to "authenticated";

grant select on table "public"."external_apps" to "authenticated";

grant trigger on table "public"."external_apps" to "authenticated";

grant truncate on table "public"."external_apps" to "authenticated";

grant update on table "public"."external_apps" to "authenticated";

grant delete on table "public"."external_apps" to "service_role";

grant insert on table "public"."external_apps" to "service_role";

grant references on table "public"."external_apps" to "service_role";

grant select on table "public"."external_apps" to "service_role";

grant trigger on table "public"."external_apps" to "service_role";

grant truncate on table "public"."external_apps" to "service_role";

grant update on table "public"."external_apps" to "service_role";

grant delete on table "public"."fields" to "anon";

grant insert on table "public"."fields" to "anon";

grant references on table "public"."fields" to "anon";

grant select on table "public"."fields" to "anon";

grant trigger on table "public"."fields" to "anon";

grant truncate on table "public"."fields" to "anon";

grant update on table "public"."fields" to "anon";

grant delete on table "public"."fields" to "authenticated";

grant insert on table "public"."fields" to "authenticated";

grant references on table "public"."fields" to "authenticated";

grant select on table "public"."fields" to "authenticated";

grant trigger on table "public"."fields" to "authenticated";

grant truncate on table "public"."fields" to "authenticated";

grant update on table "public"."fields" to "authenticated";

grant delete on table "public"."fields" to "service_role";

grant insert on table "public"."fields" to "service_role";

grant references on table "public"."fields" to "service_role";

grant select on table "public"."fields" to "service_role";

grant trigger on table "public"."fields" to "service_role";

grant truncate on table "public"."fields" to "service_role";

grant update on table "public"."fields" to "service_role";

grant delete on table "public"."generations_copies" to "anon";

grant insert on table "public"."generations_copies" to "anon";

grant references on table "public"."generations_copies" to "anon";

grant select on table "public"."generations_copies" to "anon";

grant trigger on table "public"."generations_copies" to "anon";

grant truncate on table "public"."generations_copies" to "anon";

grant update on table "public"."generations_copies" to "anon";

grant delete on table "public"."generations_copies" to "authenticated";

grant insert on table "public"."generations_copies" to "authenticated";

grant references on table "public"."generations_copies" to "authenticated";

grant select on table "public"."generations_copies" to "authenticated";

grant trigger on table "public"."generations_copies" to "authenticated";

grant truncate on table "public"."generations_copies" to "authenticated";

grant update on table "public"."generations_copies" to "authenticated";

grant delete on table "public"."generations_copies" to "service_role";

grant insert on table "public"."generations_copies" to "service_role";

grant references on table "public"."generations_copies" to "service_role";

grant select on table "public"."generations_copies" to "service_role";

grant trigger on table "public"."generations_copies" to "service_role";

grant truncate on table "public"."generations_copies" to "service_role";

grant update on table "public"."generations_copies" to "service_role";

grant delete on table "public"."organizations_usage" to "anon";

grant insert on table "public"."organizations_usage" to "anon";

grant references on table "public"."organizations_usage" to "anon";

grant select on table "public"."organizations_usage" to "anon";

grant trigger on table "public"."organizations_usage" to "anon";

grant truncate on table "public"."organizations_usage" to "anon";

grant update on table "public"."organizations_usage" to "anon";

grant delete on table "public"."organizations_usage" to "authenticated";

grant insert on table "public"."organizations_usage" to "authenticated";

grant references on table "public"."organizations_usage" to "authenticated";

grant select on table "public"."organizations_usage" to "authenticated";

grant trigger on table "public"."organizations_usage" to "authenticated";

grant truncate on table "public"."organizations_usage" to "authenticated";

grant update on table "public"."organizations_usage" to "authenticated";

grant delete on table "public"."organizations_usage" to "service_role";

grant insert on table "public"."organizations_usage" to "service_role";

grant references on table "public"."organizations_usage" to "service_role";

grant select on table "public"."organizations_usage" to "service_role";

grant trigger on table "public"."organizations_usage" to "service_role";

grant truncate on table "public"."organizations_usage" to "service_role";

grant update on table "public"."organizations_usage" to "service_role";

grant delete on table "public"."template_fields" to "anon";

grant insert on table "public"."template_fields" to "anon";

grant references on table "public"."template_fields" to "anon";

grant select on table "public"."template_fields" to "anon";

grant trigger on table "public"."template_fields" to "anon";

grant truncate on table "public"."template_fields" to "anon";

grant update on table "public"."template_fields" to "anon";

grant delete on table "public"."template_fields" to "authenticated";

grant insert on table "public"."template_fields" to "authenticated";

grant references on table "public"."template_fields" to "authenticated";

grant select on table "public"."template_fields" to "authenticated";

grant trigger on table "public"."template_fields" to "authenticated";

grant truncate on table "public"."template_fields" to "authenticated";

grant update on table "public"."template_fields" to "authenticated";

grant delete on table "public"."template_fields" to "service_role";

grant insert on table "public"."template_fields" to "service_role";

grant references on table "public"."template_fields" to "service_role";

grant select on table "public"."template_fields" to "service_role";

grant trigger on table "public"."template_fields" to "service_role";

grant truncate on table "public"."template_fields" to "service_role";

grant update on table "public"."template_fields" to "service_role";

grant delete on table "public"."templates" to "anon";

grant insert on table "public"."templates" to "anon";

grant references on table "public"."templates" to "anon";

grant select on table "public"."templates" to "anon";

grant trigger on table "public"."templates" to "anon";

grant truncate on table "public"."templates" to "anon";

grant update on table "public"."templates" to "anon";

grant delete on table "public"."templates" to "authenticated";

grant insert on table "public"."templates" to "authenticated";

grant references on table "public"."templates" to "authenticated";

grant select on table "public"."templates" to "authenticated";

grant trigger on table "public"."templates" to "authenticated";

grant truncate on table "public"."templates" to "authenticated";

grant update on table "public"."templates" to "authenticated";

grant delete on table "public"."templates" to "service_role";

grant insert on table "public"."templates" to "service_role";

grant references on table "public"."templates" to "service_role";

grant select on table "public"."templates" to "service_role";

grant trigger on table "public"."templates" to "service_role";

grant truncate on table "public"."templates" to "service_role";

grant update on table "public"."templates" to "service_role";


