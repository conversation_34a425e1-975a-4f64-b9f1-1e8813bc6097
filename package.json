{"name": "next-supabase-saas-kit", "version": "0.10.27", "private": true, "sideEffects": false, "scripts": {"analyze": "ANALYZE=true next build", "dev": "next dev | pino-pretty", "dev:test": "NODE_ENV=test next dev", "build": "next build", "start": "next start", "lint": "next lint", "postbuild": "next-sitemap", "cypress": "NODE_ENV=test cypress open", "cypress:headless": "NODE_ENV=test cypress run", "supabase:start": "supabase start --ignore-health-check", "supabase:stop": "supabase stop", "supabase:stop:no-backup": "supabase stop --no-backup", "supabase:db:reset": "supabase db reset", "format": "prettier --write .", "stripe:listen": "docker run --rm -it --name=stripe -v ~/.config/stripe:/root/.config/stripe stripe/stripe-cli:latest listen --forward-to http://host.docker.internal:3000/api/stripe/webhook", "stripe:mock-server": "docker run --rm -it -p 12111-12112:12111-12112 stripe/stripe-mock:latest", "typegen": "supabase gen types typescript --local > src/database.types.ts", "typecheck": "tsc -b && tsc -b cypress", "test:e2e": "sh ./scripts/test.sh", "test:db": "supabase test db", "test:reset:db": "supabase db reset && supabase test db --debug"}, "prettier": {"tabWidth": 2, "useTabs": false, "semi": true, "printWidth": 80, "singleQuote": true}, "dependencies": {"@heroicons/react": "^2.1.1", "@next/bundle-analyzer": "^14.1.0", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-popover": "1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@react-email/components": "^0.0.13", "@sentry/node": "^7.93.0", "@sentry/react": "^7.93.0", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.3.0", "@supabase/gotrue-js": "^2.70.0", "@supabase/ssr": "^0.0.10", "@supabase/supabase-js": "^2.39.3", "@tanstack/react-table": "^8.11.6", "classnames": "^2.5.1", "clsx": "^2.1.0", "cmdk": "^0.2.0", "contentlayer": "0.3.4", "cva": "npm:class-variance-authority@^0.7.0", "dayjs": "^1.11.10", "edge-csrf": "^1.0.7", "handlebars": "^4.7.8", "heroicons": "^2.1.1", "i18next": "23.7.16", "i18next-browser-languagedetector": "^7.2.0", "i18next-http-backend": "^2.4.2", "i18next-resources-to-backend": "^1.2.0", "lodash": "^4.17.21", "nanoid": "^5.0.4", "next": "14.1.0", "next-contentlayer": "0.3.4", "next-sitemap": "^4.2.3", "nodemailer": "^6.9.8", "openai": "^4.26.0", "pino": "^8.17.2", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.49.3", "react-i18next": "^14.0.0", "react-markdown": "^9.0.1", "react-top-loading-bar": "^2.3.1", "recharts": "^2.10.4", "rehype-autolink-headings": "^7.1.0", "rehype-slug": "^6.0.0", "server-only": "^0.0.1", "sonner": "^1.3.1", "stripe": "^14.13.0", "swr": "^2.2.4", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "tiny-invariant": "^1.3.1", "zod": "^3.22.4"}, "devDependencies": {"@types/eslint": "^8.56.2", "@types/i18next-fs-backend": "^1.1.5", "@types/lodash": "^4.14.202", "@types/node": "20.11.5", "@types/nodemailer": "^6.4.14", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.17", "cypress": "13.6.3", "encoding": "^0.1.13", "eslint": "8.56.0", "eslint-config-next": "14.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-cypress": "^2.15.1", "pino-pretty": "10.3.1", "postcss": "^8.4.33", "prettier": "3.2.4", "prettier-plugin-tailwindcss": "^0.5.11", "supabase": "^1.131.5", "tailwindcss": "^3.4.1", "typescript": "5.3.3"}, "engines": {"node": ">=18.17.0 <22.6.0"}, "overrides": {"@typescript-eslint/parser": "6.13.2"}}