const GithubLogo: React.FCC<{ className?: string; fill?: string }> = ({
  className,
  fill,
}) => {
  return (
    <svg
      viewBox="0 0 16 16"
      version="1.1"
      width="24"
      height="24"
      className={className ?? ``}
    >
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g fill={fill}>
          <path d="M8.20003216,0 C3.67186256,0 0,3.67233045 0,8.2025137 C0,11.8266603 2.34955472,14.9012922 5.60770169,15.9859115 C6.01749428,16.0618219 6.16798309,15.8079721 6.16798309,15.5913056 C6.16798309,15.3957396 6.16033003,14.7495356 6.15685721,14.064154 C3.87553694,14.5603383 3.3941657,13.0963596 3.3941657,13.0963596 C3.02115925,12.1482506 2.48370842,11.896202 2.48370842,11.896202 C1.7397535,11.3870871 2.53978801,11.3975087 2.53978801,11.3975087 C3.36323189,11.4554066 3.79681972,12.242817 3.79681972,12.242817 C4.52816959,13.4968196 5.71510182,13.1342505 6.18303197,12.9247247 C6.25660428,12.3945093 6.46915364,12.0327122 6.70363321,11.8278826 C4.88233278,11.6204154 2.96765212,10.9170854 2.96765212,7.77413412 C2.96765212,6.87864774 3.28798748,6.14688355 3.81257603,5.57247278 C3.72742766,5.36584187 3.44677247,4.53159853 3.89200067,3.40175461 C3.89200067,3.40175461 4.58058343,3.18129256 6.14759636,4.24255971 C6.80164386,4.06076054 7.50315322,3.96966796 8.20003216,3.96658007 C8.89691109,3.96966796 9.59893494,4.06076054 10.2542687,4.24255971 C11.8194166,3.18129256 12.5070347,3.40175461 12.5070347,3.40175461 C12.9533562,4.53159853 12.6725723,5.36584187 12.587424,5.57247278 C13.1131701,6.14688355 13.4313189,6.87858341 13.4313189,7.77413412 C13.4313189,10.9245478 11.5130368,11.6182925 9.68710595,11.8213208 C9.98120223,12.0758783 10.2432714,12.5750862 10.2432714,13.3403025 C10.2432714,14.4377879 10.2337533,15.3211158 10.2337533,15.5913056 C10.2337533,15.8095804 10.3813481,16.0653602 10.7970574,15.9848178 C14.0534036,14.8989763 16.4,11.825438 16.4,8.2025137 C16.4,3.67233045 12.7286519,0 8.20003216,0" />
          <path
            d="M3.07518929,11.6824029 C3.05731167,11.723257 2.99359073,11.7355197 2.93568001,11.7075092 C2.87662002,11.6806603 2.84341872,11.6248974 2.86257332,11.5838496 C2.88013169,11.5417693 2.94385264,11.5300229 3.00278493,11.5582916 C3.06197262,11.5850759 3.09568471,11.6413552 3.07518929,11.6824029 M3.47564797,12.0435714 C3.436828,12.0799722 3.36091196,12.0630626 3.30938611,12.0055571 C3.25613634,11.9481806 3.24617596,11.8715065 3.28557057,11.8345248 C3.32560367,11.7981885 3.39922115,11.8151627 3.45259861,11.8726037 C3.50584838,11.9306256 3.51619186,12.0068479 3.47558413,12.0436359 M3.75038868,12.5056811 C3.70045904,12.5407266 3.61886048,12.5078755 3.5684839,12.4346866 C3.51861811,12.3615623 3.51861811,12.2737872 3.56956933,12.2386127 C3.62013745,12.2034381 3.70045904,12.235063 3.75153796,12.307671 C3.8013399,12.3820861 3.8013399,12.4698612 3.75032483,12.5057457 M4.2149514,13.0409152 C4.1703212,13.0906114 4.07531442,13.0773161 4.0057194,13.0094195 C3.93459201,12.9430719 3.91473508,12.8489074 3.95949298,12.7991467 C4.00463397,12.7493214 4.10021539,12.7633267 4.1703212,12.8306424 C4.24100164,12.8968609 4.26258248,12.9917354 4.21501525,13.0409152 M4.81538402,13.2216286 C4.79578249,13.2860399 4.70422354,13.3153413 4.6120261,13.2879762 C4.51995636,13.259772 4.45968324,13.1842596 4.4782632,13.1191383 C4.49741779,13.0542751 4.58935983,13.0237475 4.68225961,13.0530489 C4.77420165,13.081124 4.83460247,13.1560554 4.81544787,13.2216286 M5.49881989,13.2982381 C5.50111844,13.3661347 5.42290385,13.422414 5.32610931,13.4236402 C5.22874013,13.4257701 5.15001476,13.3708461 5.14899318,13.3041113 C5.14899318,13.2355693 5.22542,13.1798063 5.32272533,13.1781928 C5.41951987,13.1762566 5.49881989,13.2307933 5.49881989,13.2982381 M6.17012451,13.2722283 C6.18174496,13.3384468 6.11444849,13.4064725 6.01835628,13.4245438 C5.92386029,13.4419697 5.83638766,13.4011156 5.82432026,13.3354779 C5.81257211,13.2675814 5.88114555,13.1996203 5.97545,13.1820007 C6.07173375,13.1650911 6.15786557,13.2049126 6.17012451,13.2722283"
            fillRule="nonzero"
          />
        </g>
      </g>
    </svg>
  );
};

export default GithubLogo;
